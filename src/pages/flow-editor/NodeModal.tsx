import React, { useState, useEffect } from 'react';
import * as Icons from 'lucide-react';
import type { NodeData } from '@/service/nodeService';
import NodeParametersForm from './NodeParametersForm';
import { DragDropProvider, useDragDrop } from '@/contexts/DragDropContext';
import DataTree from '@/components/DataTree';
import { calculateNodeExecutionData, filterNodeMetadata } from '@/utils/expressionUtils';

interface NodeModalProps {
  node: NodeData;
  isOpen: boolean;
  onClose: () => void;
  onSave: (parameters: Record<string, unknown>, executionData?: any, inputData?: string) => void;
  connectedNodes: {
    inputs: (NodeData & Record<string, unknown>)[];
    outputs: (NodeData & Record<string, unknown>)[];
  };
}

// Inner component that uses the drag-drop context
const NodeModalContent: React.FC<{
  node: NodeData;
  onClose: () => void;
  onSave: (parameters: Record<string, unknown>, executionData?: any, inputData?: string) => void;
  connectedNodes: {
    inputs: (NodeData & Record<string, unknown>)[];
    outputs: (NodeData & Record<string, unknown>)[];
  };
}> = ({ node, onClose, onSave, connectedNodes }) => {
  const [activeTab, setActiveTab] = useState<'previous' | 'parameters' | 'next'>('parameters');
  const { setAvailableNodes } = useDragDrop();
  const [currentAvailableNodes, setCurrentAvailableNodes] = useState<any[]>([]);
  const [inputData, setInputData] = useState<string>('');

  // Check if this is the first node (no input connections)
  const isFirstNode = connectedNodes.inputs.length === 0;

  // Initialize input data from node's existing input data
  useEffect(() => {
    if (isFirstNode && node.inputData) {
      setInputData(typeof node.inputData === 'string' ? node.inputData : JSON.stringify(node.inputData, null, 2));
    }
  }, [isFirstNode, node.inputData]);

  // Set available nodes for expression evaluation
  useEffect(() => {


    let availableNodesData = connectedNodes.inputs.map(inputNode => {
      // Use real execution data if available, otherwise create a basic structure
      let executionData = inputNode.executionData;

      if (!executionData?.output) {
        // If no execution data, create a basic structure that can be used for expressions
        // This allows expressions to be written even before the workflow is run
        executionData = {
          output: {
            // Add some common fields that might be expected
            id: `${inputNode.id}_output`,
            name: inputNode.label || inputNode.nodeType.display_name,
            status: 'pending',
            // Add any default values from the node type if available
            ...inputNode.parameters
          }
        };
      }

      return {
        id: inputNode.id,
        label: inputNode.label,
        nodeType: {
          display_name: inputNode.nodeType.display_name
        },
        executionData: executionData
      };
    });

    // For first nodes, add the current node with input data as available for expressions
    if (isFirstNode && inputData.trim()) {
      try {
        const parsedInputData = JSON.parse(inputData);
        availableNodesData = [{
          id: node.id,
          label: node.label || node.nodeType.display_name,
          nodeType: {
            display_name: node.nodeType.display_name
          },
          executionData: {
            output: parsedInputData
          }
        }];
      } catch (error) {
        // If JSON is invalid, don't add the node
        console.warn('Invalid JSON in input data, not adding to available nodes:', error);
      }
    }



    setAvailableNodes(availableNodesData);
    setCurrentAvailableNodes(availableNodesData);
  }, [connectedNodes.inputs, setAvailableNodes, isFirstNode, inputData, node.id, node.label, node.nodeType.display_name]);

  const handleSave = (parameters: Record<string, unknown>) => {
    // Calculate execution data by evaluating expressions
    const { evaluatedParameters, executionData } = calculateNodeExecutionData(
      parameters,
      currentAvailableNodes
    );



    // For first nodes, include input data in execution data
    let finalExecutionData = executionData;
    if (isFirstNode && inputData.trim()) {
      try {
        const parsedInputData = JSON.parse(inputData);
        finalExecutionData = {
          ...executionData,
          output: {
            ...(executionData.output || {}),
            ...parsedInputData // Merge input data into output
          }
        };
      } catch (error) {
        console.error('Invalid JSON in input data:', error);
        // Keep original execution data if JSON is invalid
      }
    }

    // Save parameters, execution data, and input data
    onSave(parameters, finalExecutionData, isFirstNode ? inputData : undefined);
    onClose();
  };

  const getIcon = (iconName: string) => {
    const iconKey = iconName
      ?.split('-')
      ?.map(word => word.charAt(0).toUpperCase() + word.slice(1))
      ?.join('');

    const IconsMap = Icons as unknown as Record<string, React.ComponentType<{ size?: number; className?: string }>>;
    return IconsMap[iconKey] || Icons.Circle;
  };

  const IconComponent = getIcon(node.nodeType.icon);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden border border-gray-200">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div
              className="w-10 h-10 rounded-full flex items-center justify-center"
              style={{ backgroundColor: node.nodeType.icon_color }}
            >
              <IconComponent size={20} className="text-white" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {node.label || node.nodeType.display_name}
              </h2>
              <p className="text-sm text-gray-500">
                {node.nodeType.description}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <Icons.X size={20} className="text-gray-500" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 bg-gray-100 px-4">
          <nav className="flex space-x-6">
            {(['previous', 'parameters', 'next'] as const).map(tab => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`relative py-3 px-4 rounded-t-md text-sm font-medium transition-colors duration-200 ${activeTab === tab
                  ? 'bg-white text-blue-600 border-b-2 border-blue-500'
                  : 'text-gray-500 hover:bg-white hover:text-gray-800'
                  }`}
              >
                {tab === 'previous' ? (isFirstNode ? 'Input Data' : 'Previous Nodes') : tab === 'next' ? 'Next Nodes' : 'Parameters'}{' '}
                ({tab === 'parameters' ? (node.nodeType.parameters?.length || 0) :
                  tab === 'previous' ? connectedNodes.inputs.length : connectedNodes.outputs.length})
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex h-[60vh] bg-white">
          {/* Left Panel - Inputs */}
          <div className="w-1/3 p-4 border-gray-200 bg-gray-50 overflow-y-auto rounded-bl-2xl">
            <div className="bg-white rounded-lg shadow p-4 h-full flex flex-col">
              <h3 className="text-sm font-semibold text-gray-900 mb-4">
                {isFirstNode ? 'Input Data' : 'Previous Node Output'}
              </h3>
              <div className="space-y-3">
                {connectedNodes.inputs.length > 0 ? (
                  <div className="space-y-4">
                    {connectedNodes.inputs.map((inputNode, index) => (
                      <div key={index} className="bg-white p-4 rounded-lg border">
                        <h4 className="text-md font-medium text-gray-900 mb-3">
                          {inputNode.label || inputNode.nodeType.display_name}
                        </h4>
                        <DataTree
                          nodeId={inputNode.id}
                          nodeName={inputNode.label || inputNode.nodeType.display_name}
                          data={filterNodeMetadata(inputNode.executionData?.output)}
                          maxDepth={3}
                        />
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No previous nodes connected</p>
                )}
              </div>
            </div>
          </div>

          {/* Center Panel */}
          <div className="flex-1 p-4 overflow-y-auto ">
            {activeTab === 'previous' && (
              <div className="space-y-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800">
                  {isFirstNode ? 'Input Data' : 'Previous Node'}
                </h3>
                {isFirstNode ? (
                  // Input Data Editor for first nodes
                  <div className="bg-white p-4 rounded-lg border">
                    <div className="mb-4">
                      <p className="text-sm text-gray-600 mb-4">
                        Provide JSON input data that will be available for expressions in subsequent nodes.
                      </p>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          JSON Input Data
                        </label>
                        <textarea
                          value={inputData}
                          onChange={(e) => setInputData(e.target.value)}
                          placeholder={`[
  {
    "name": "First item",
    "code": 1
  },
  {
    "name": "Second item",
    "code": 2
  }
]`}
                          className="w-full h-64 p-3 border border-gray-300 rounded-lg font-mono text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          style={{ resize: 'vertical' }}
                        />
                      </div>
                      {inputData.trim() && (
                        <div className="mt-4">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Preview:</h4>
                          <div className="bg-gray-50 p-3 rounded border">
                            <DataTree
                              nodeId={node.id}
                              nodeName="Input Data"
                              data={(() => {
                                try {
                                  return JSON.parse(inputData);
                                } catch {
                                  return { error: 'Invalid JSON format' };
                                }
                              })()}
                              maxDepth={3}
                            />
                          </div>
                        </div>
                      )}
                      {inputData.trim() && (
                        <div className="flex justify-end mt-4">
                          <button
                            type="button"
                            onClick={() => {
                              try {
                                JSON.parse(inputData); // Validate JSON
                                // Save just the input data for the first node
                                onSave({}, undefined, inputData);
                                onClose();
                              } catch (error) {
                                alert('Please provide valid JSON input data before saving.');
                              }
                            }}
                            className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-green-500 to-emerald-600 rounded-md hover:from-green-600 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                          >
                            Save and Pin Data
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  // Connected input nodes for non-first nodes
                  connectedNodes.inputs.length > 0 ? (
                    connectedNodes.inputs.map((inputNode, index) => {
                      const InputIcon = getIcon(inputNode.nodeType.icon);
                      return (
                        <div
                          key={index}
                          className="flex items-center space-x-3 p-3 bg-white rounded-lg shadow-sm border border-gray-100"
                        >
                          <div
                            className="w-8 h-8 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: inputNode.nodeType.icon_color }}
                          >
                            <InputIcon size={14} className="text-white" />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              {inputNode.label || inputNode.nodeType.display_name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {inputNode.nodeType.description}
                            </p>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <p className="text-sm text-gray-500">No connected input nodes</p>
                  )
                )}
              </div>
            )}

            {activeTab === 'parameters' && (
              <div className="space-y-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800">Node Parameters</h3>
                <NodeParametersForm node={node} onSave={handleSave} onCancel={onClose} />
              </div>
            )}

            {activeTab === 'next' && (
              <div className="space-y-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800">Next Node</h3>
                {connectedNodes.outputs.length > 0 ? (
                  connectedNodes.outputs.map((outputNode, index) => {
                    const OutputIcon = getIcon(outputNode.nodeType.icon);
                    return (
                      <div
                        key={index}
                        className="flex items-center space-x-3 p-3 bg-white rounded-lg shadow-sm border border-gray-100"
                      >
                        <div
                          className="w-8 h-8 rounded-full flex items-center justify-center"
                          style={{ backgroundColor: outputNode.nodeType.icon_color }}
                        >
                          <OutputIcon size={14} className="text-white" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {outputNode.label || outputNode.nodeType.display_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {outputNode.nodeType.description}
                          </p>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <p className="text-sm text-gray-500">No connected output nodes</p>
                )}
                {node.executionData?.error && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <div className="flex items-center space-x-2">
                      <Icons.AlertCircle className="w-4 h-4 text-red-600" />
                      <p className="text-sm text-red-700">{node.executionData.error}</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Right Panel - Outputs */}
          <div className="w-1/3 p-4 border-gray-200 bg-gray-50 overflow-y-auto rounded-br-2xl">
            <div className="bg-white rounded-lg shadow p-4 h-full flex flex-col">
              <h3 className="text-sm font-semibold text-gray-900 mb-4">Current Node Output</h3>
              <div className="space-y-3">
                <div className="bg-white p-4 rounded-lg border">
                  <DataTree
                    nodeId={node.id}
                    nodeName={node.label || node.nodeType.display_name}
                    data={filterNodeMetadata(node.executionData?.output || {})}
                    maxDepth={3}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const NodeModal: React.FC<NodeModalProps> = ({
  node,
  isOpen,
  onClose,
  onSave,
  connectedNodes
}) => {
  if (!isOpen) return null;

  return (
    <DragDropProvider>
      <NodeModalContent
        node={node}
        onClose={onClose}
        onSave={onSave}
        connectedNodes={connectedNodes}
      />
    </DragDropProvider>
  );
};

export default NodeModal;
